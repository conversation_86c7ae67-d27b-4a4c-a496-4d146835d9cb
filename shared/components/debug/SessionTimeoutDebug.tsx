// shared/components/debug/SessionTimeoutDebug.tsx
"use client";

import React, { useState, useEffect } from 'react';
import { useAuthStore } from '@/shared/stores/authStore';
import { useBetshopSettings } from '@/shared/hooks/business/useBetshopSettings';
import { useSessionTimeoutContext } from '@/shared/providers/SessionTimeoutProvider';

interface SessionTimeoutDebugProps {
  enabled?: boolean;
}

export const SessionTimeoutDebug: React.FC<SessionTimeoutDebugProps> = ({ 
  enabled = process.env.NODE_ENV === 'development' 
}) => {
  const { user, isAuthenticated } = useAuthStore();
  const { cashierWebTimeout, isLoading, error, settings } = useBetshopSettings(user?.tenantId);
  const { remainingTime, isSessionExpired, isWarningShown } = useSessionTimeoutContext();
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every second
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  if (!enabled) {
    return null;
  }

  const formatTime = (ms: number) => {
    const totalSeconds = Math.floor(ms / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const timeoutSetting = settings.find(s => s.key === 'cashier_web_timeout');

  return (
    <div 
      style={{
        position: 'fixed',
        top: '10px',
        right: '10px',
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '15px',
        borderRadius: '8px',
        fontSize: '12px',
        fontFamily: 'monospace',
        zIndex: 9999,
        maxWidth: '350px',
        border: '1px solid #333'
      }}
    >
      <div style={{ fontWeight: 'bold', marginBottom: '10px', color: '#4CAF50' }}>
        🕐 Session Timeout Debug
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Current Time:</strong> {currentTime.toLocaleTimeString()}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Authenticated:</strong> {isAuthenticated ? '✅' : '❌'}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>User Tenant ID:</strong> {user?.tenantId || 'Not set'}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Settings Loading:</strong> {isLoading ? '⏳' : '✅'}
      </div>
      
      {error && (
        <div style={{ marginBottom: '8px', color: '#f44336' }}>
          <strong>Settings Error:</strong> {error.message}
        </div>
      )}
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Settings Count:</strong> {settings.length}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Timeout Setting Found:</strong> {timeoutSetting ? '✅' : '❌'}
      </div>
      
      {timeoutSetting && (
        <div style={{ marginBottom: '8px' }}>
          <strong>Timeout Setting Value:</strong> {timeoutSetting.value} minutes
        </div>
      )}
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Effective Timeout:</strong> {cashierWebTimeout} minutes
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Remaining Time:</strong> {formatTime(remainingTime)}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Session Expired:</strong> {isSessionExpired ? '🚨 YES' : '✅ No'}
      </div>
      
      <div style={{ marginBottom: '8px' }}>
        <strong>Warning Shown:</strong> {isWarningShown ? '⚠️ YES' : '✅ No'}
      </div>
      
      <div style={{ 
        marginTop: '10px', 
        padding: '8px', 
        background: 'rgba(255, 255, 255, 0.1)', 
        borderRadius: '4px',
        fontSize: '11px'
      }}>
        <div><strong>Expected behavior:</strong></div>
        <div>• Session should expire after {cashierWebTimeout} minutes of inactivity</div>
        <div>• Warning should show {2} minutes before expiry</div>
        <div>• Any user activity resets the timer</div>
      </div>
    </div>
  );
};

export default SessionTimeoutDebug;
