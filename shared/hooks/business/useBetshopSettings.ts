// shared/hooks/business/useBetshopSettings.ts
import { useAuthStore } from '@/shared/stores/authStore';
import { useQuery } from '@tanstack/react-query';

interface BetshopSettingsRequest {
  tenantID: number;
}

interface BetshopSettingsItem {
  id: string;
  key: string;
  value: string;
  description?: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

interface BetshopSettingsResponse {
  success: number;
  message: string;
  data: {
    settingsDetails: BetshopSettingsItem[];
  };
  errors?: string[];
}

interface BetshopSettingsError {
  success: number;
  message: string;
  errors?: string[];
}

/**
 * Fetch betshop settings from the reporting API
 */
const fetchBetshopSettings = async (tenantId: number): Promise<BetshopSettingsResponse> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  if (!tenantId) {
    throw new Error('Tenant ID is required');
  }

  // Use the reporting API URL
  const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL || 'https://reporting.ingrandstation.com';

  const requestBody: BetshopSettingsRequest = {
    tenantID: tenantId
  };

  const response = await fetch(`${baseUrl}/api/v2/admin/betshop-settings`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorData: BetshopSettingsError = await response.json();
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  const data: BetshopSettingsResponse = await response.json();

  if (data.success !== 1) {
    throw new Error(data.message || 'Failed to fetch betshop settings');
  }

  return data;
};

/**
 * Extract cashier web timeout value from settings
 */
const extractCashierWebTimeout = (settings: BetshopSettingsItem[]): number => {
  const timeoutSetting = settings.find(setting => setting.key === 'cashier_web_timeout');

  if (!timeoutSetting) {
    // Default to 30 minutes if setting not found
    return 30;
  }

  const timeoutValue = parseInt(timeoutSetting.value, 10);

  // Validate the timeout value (should be between 1 and 480 minutes - 8 hours max)
  if (isNaN(timeoutValue) || timeoutValue < 1 || timeoutValue > 480) {
    // Default to 30 minutes if invalid value
    return 30;
  }

  return timeoutValue;
};

/**
 * Hook to fetch betshop settings and extract cashier web timeout
 */
export const useBetshopSettings = (tenantId?: number) => {
  const { user, isAuthenticated } = useAuthStore();

  // Use tenantId from parameter or fallback to user's tenantId
  const effectiveTenantId = tenantId || user?.tenantId;

  const query = useQuery<BetshopSettingsResponse>({
    queryKey: ['betshopSettings', effectiveTenantId],
    queryFn: () => fetchBetshopSettings(effectiveTenantId!),
    enabled: isAuthenticated && !!effectiveTenantId,
    staleTime: 10 * 60 * 1000, // 10 minutes - settings don't change frequently
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Extract cashier web timeout from settings
  const cashierWebTimeout = query.data?.data?.settingsDetails
    ? extractCashierWebTimeout(query.data.data.settingsDetails)
    : 30; // Default fallback changed from 720 to 30 minutes

  // Debug logging for session timeout
  if (process.env.NODE_ENV === 'development') {
    console.log('Session Timeout Debug:', {
      effectiveTenantId,
      isAuthenticated,
      queryEnabled: isAuthenticated && !!effectiveTenantId,
      queryStatus: query.status,
      queryError: query.error?.message,
      settingsCount: query.data?.data?.settingsDetails?.length || 0,
      cashierWebTimeout,
      hasTimeoutSetting: query.data?.data?.settingsDetails?.some(s => s.key === 'cashier_web_timeout'),
      timeoutSettingValue: query.data?.data?.settingsDetails?.find(s => s.key === 'cashier_web_timeout')?.value
    });
  }

  return {
    ...query,
    cashierWebTimeout,
    settings: query.data?.data?.settingsDetails || [],
  };
};

/**
 * Hook specifically for getting cashier web timeout
 * This is a convenience hook that only returns the timeout value
 */
export const useCashierWebTimeout = (tenantId?: number) => {
  const { cashierWebTimeout, isLoading, error } = useBetshopSettings(tenantId);

  return {
    timeout: cashierWebTimeout,
    isLoading,
    error,
  };
};
